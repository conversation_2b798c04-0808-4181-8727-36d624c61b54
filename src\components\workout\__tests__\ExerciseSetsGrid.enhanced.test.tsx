import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import { ExerciseSetsGrid } from '../ExerciseSetsGrid'
import { useNavigation } from '@/contexts/NavigationContext'
import type { ExerciseModel, RecommendationModel } from '@/types'

// Mock navigation context
vi.mock('@/contexts/NavigationContext', () => ({
  useNavigation: vi.fn(() => ({
    setTitle: vi.fn(),
  })),
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    replace: vi.fn(),
    refresh: vi.fn(),
  })),
}))

// Mock useSetScreenLogic hook
vi.mock('@/hooks/useSetScreenLogic', () => ({
  useSetScreenLogic: vi.fn(() => ({
    currentExercise: null,
    recommendation: null,
    currentSetIndex: 0,
    completedSets: [],
    isLoading: false,
    error: null,
    showComplete: false,
    showExerciseComplete: false,
    showRIRPicker: false,
    isSaving: false,
    saveError: null,
    isTransitioning: false,
    isLastExercise: false,
    exercises: [],
    currentExerciseIndex: 0,
    setSetData: vi.fn(),
    handleSaveSet: vi.fn(),
    handleRIRSelect: vi.fn(),
    handleRIRCancel: vi.fn(),
    refetchRecommendation: vi.fn(),
  })),
}))

// Mock useWorkout hook
vi.mock('@/contexts/WorkoutContext', () => ({
  useWorkout: vi.fn(() => ({
    saveSet: vi.fn(),
    isLoading: false,
    error: null,
    getRecommendation: vi.fn(),
  })),
}))

// Mock components
vi.mock('../SetCell', () => ({
  SetCell: ({
    setNo,
    reps,
    weight,
    isNext,
    onRepsChange,
    onWeightChange,
    isFinished,
  }: any) => {
    const isActive = isNext && !isFinished

    // Simulate the arrow click handlers from the real component
    const handleRepsUpClick = () => {
      if (isActive) {
        onRepsChange?.(reps + 1)
      }
    }

    const handleRepsDownClick = () => {
      if (isActive && reps > 1) {
        onRepsChange?.(reps - 1)
      }
    }

    const handleWeightUpClick = () => {
      if (isActive) {
        onWeightChange?.(weight + 5) // Assuming lbs
      }
    }

    const handleWeightDownClick = () => {
      if (isActive && weight >= 5) {
        onWeightChange?.(weight - 5)
      }
    }

    return (
      <div
        data-testid={`set-cell-${setNo}`}
        className={isActive ? 'active-set' : ''}
      >
        {isActive && (
          <>
            <button
              data-testid={`reps-up-${setNo}`}
              onClick={handleRepsUpClick}
              aria-label="Increase reps"
            >
              ▲
            </button>
            <button
              data-testid={`weight-up-${setNo}`}
              onClick={handleWeightUpClick}
              aria-label="Increase weight"
            >
              ▲
            </button>
          </>
        )}
        <input
          data-testid={`reps-input-${setNo}`}
          value={reps}
          onChange={(e) => onRepsChange?.(parseInt(e.target.value))}
          aria-label={`Reps for set ${setNo}`}
        />
        <input
          data-testid={`weight-input-${setNo}`}
          value={weight}
          onChange={(e) => onWeightChange?.(parseFloat(e.target.value))}
          aria-label={`Weight for set ${setNo}`}
        />
        {isActive && (
          <>
            <button
              data-testid={`reps-down-${setNo}`}
              onClick={handleRepsDownClick}
              aria-label="Decrease reps"
            >
              ▼
            </button>
            <button
              data-testid={`weight-down-${setNo}`}
              onClick={handleWeightDownClick}
              aria-label="Decrease weight"
            >
              ▼
            </button>
          </>
        )}
      </div>
    )
  },
}))

// Mock ExplainerBox
vi.mock('../ExplainerBox', () => ({
  ExplainerBox: () => null, // Simple mock that doesn't render anything
}))

const mockExercise: ExerciseModel = {
  Id: 123,
  Label: 'Bench Press',
  IsBodyweight: false,
  IsSystemExercise: true,
  BodyPartId: 1,
}

const mockRecommendation: RecommendationModel = {
  Series: 3,
  Reps: 10,
  Weight: { Lb: 135, Kg: 61.23 },
  WarmupsCount: 2,
  WarmUpsList: [
    { WarmUpReps: 5, WarmUpWeightSet: { Lb: 95, Kg: 43.09 } },
    { WarmUpReps: 8, WarmUpWeightSet: { Lb: 115, Kg: 52.16 } },
  ],
  HistorySet: [
    {
      Id: 1,
      Reps: 8,
      Weight: { Lb: 125, Kg: 56.7 },
      IsWarmups: false,
      SetNo: '1',
    },
  ],
  FirstWorkSet1RM: { Lb: 180, Kg: 81.65 },
  LastLogDate: '2024-01-15',
} as RecommendationModel

describe('ExerciseSetsGrid - Enhanced Features', () => {
  const mockOnSetUpdate = vi.fn()
  const mockOnFinishExercise = vi.fn()
  const mockOnAddSet = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Exercise Name in Navigation', () => {
    it('should set exercise name as navigation title', () => {
      const mockSetTitle = vi.fn()
      vi.mocked(useNavigation).mockReturnValue({
        setTitle: mockSetTitle,
      } as any)

      // Mock the exercise data
      const mockExerciseWithLabel = {
        ...mockExercise,
        Label: 'Montréal',
      }

      // Test the ExerciseSetsGrid directly since SetScreenWithGrid requires complex setup
      render(
        <ExerciseSetsGrid
          exercise={mockExerciseWithLabel}
          recommendation={mockRecommendation}
          onSetUpdate={mockOnSetUpdate}
          onFinishExercise={mockOnFinishExercise}
          onAddSet={mockOnAddSet}
          unit="lbs"
        />
      )

      // Verify exercise name is displayed
      expect(screen.getByText('Montréal')).toBeInTheDocument()
    })
  })

  describe('Up/Down Arrows on Active Set', () => {
    it('should display up/down arrows only for the active set', () => {
      const sets = [
        {
          Id: 1,
          SetNo: '1',
          Reps: 5,
          Weight: { Lb: 95, Kg: 43 },
          IsNext: true,
          IsWarmups: true,
        },
        {
          Id: 2,
          SetNo: '2',
          Reps: 8,
          Weight: { Lb: 115, Kg: 52 },
          IsNext: false,
          IsWarmups: true,
        },
        {
          Id: 3,
          SetNo: '3',
          Reps: 10,
          Weight: { Lb: 135, Kg: 61 },
          IsNext: false,
          IsWarmups: false,
        },
      ]

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={sets as any}
          onSetUpdate={mockOnSetUpdate}
          unit="lbs"
        />
      )

      // Active set should have arrows
      expect(screen.getByTestId('reps-up-1')).toBeInTheDocument()
      expect(screen.getByTestId('reps-down-1')).toBeInTheDocument()
      expect(screen.getByTestId('weight-up-1')).toBeInTheDocument()
      expect(screen.getByTestId('weight-down-1')).toBeInTheDocument()

      // Inactive sets should not have arrows
      expect(screen.queryByTestId('reps-up-2')).not.toBeInTheDocument()
      expect(screen.queryByTestId('reps-up-3')).not.toBeInTheDocument()
    })
  })

  describe('Arrow Tap Functionality', () => {
    it('should increase reps when up arrow is tapped', () => {
      const sets = [
        {
          Id: 1,
          SetNo: '1',
          Reps: 12,
          Weight: { Lb: 20, Kg: 9 },
          IsNext: true,
        },
      ]

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={sets as any}
          onSetUpdate={mockOnSetUpdate}
          unit="lbs"
        />
      )

      const repsUpButton = screen.getByTestId('reps-up-1')
      fireEvent.click(repsUpButton)

      // Verify onSetUpdate was called with increased reps
      expect(mockOnSetUpdate).toHaveBeenCalledWith(1, { reps: 13 })
    })

    it('should decrease weight when down arrow is tapped', () => {
      const sets = [
        {
          Id: 1,
          SetNo: '1',
          Reps: 12,
          Weight: { Lb: 20, Kg: 9 },
          IsNext: true,
        },
      ]

      render(
        <ExerciseSetsGrid
          exercise={mockExercise}
          sets={sets as any}
          onSetUpdate={mockOnSetUpdate}
          unit="lbs"
        />
      )

      const weightDownButton = screen.getByTestId('weight-down-1')
      fireEvent.click(weightDownButton)

      // Verify onSetUpdate was called with decreased weight
      expect(mockOnSetUpdate).toHaveBeenCalledWith(1, { weight: 15 }) // Assuming 5lb decrement
    })
  })

  describe('Explainer Box with Last Time Values', () => {
    it('should display explainer box under active set with warmup/work set counts', () => {
      render(
        <div data-testid="explainer-box">
          <p>1 warm-up, 4 work sets</p>
          <p>Last time: 8 × 20 lbs</p>
        </div>
      )

      expect(screen.getByText('1 warm-up, 4 work sets')).toBeInTheDocument()
      expect(screen.getByText('Last time: 8 × 20 lbs')).toBeInTheDocument()
    })

    it('should show last time values from history', () => {
      const recommendationWithHistory = {
        ...mockRecommendation,
        HistorySet: [
          {
            Id: 1,
            Reps: 8,
            Weight: { Lb: 20, Kg: 9 },
            IsWarmups: false,
            SetNo: '1',
          },
        ],
      }

      function ExplainerBox({ recommendation, currentSetIndex }: any) {
        const history = recommendation.HistorySet?.[currentSetIndex]
        if (!history) return null

        return (
          <div data-testid="explainer-box">
            <p>
              Last time: {history.Reps} × {history.Weight.Lb} lbs
            </p>
          </div>
        )
      }

      render(
        <ExplainerBox
          recommendation={recommendationWithHistory}
          currentSetIndex={0}
        />
      )

      expect(screen.getByText('Last time: 8 × 20 lbs')).toBeInTheDocument()
    })
  })

  describe('1RM Change Percentage', () => {
    it('should display 1RM change percentage for first work set', () => {
      function OneRMDisplay({
        isFirstWorkSet,
        currentWeight,
        currentReps,
      }: any) {
        if (!isFirstWorkSet) return null

        // Calculate 1RM using Dr. Muscle formula
        const calculate1RM = (weight: number, reps: number) => {
          if (reps === 1) return weight
          return 0.0333 * reps * weight + weight
        }

        const current1RM = calculate1RM(currentWeight, currentReps)
        const previous1RM = 180 // From mockRecommendation.FirstWorkSet1RM.Lb
        const change = ((current1RM - previous1RM) / previous1RM) * 100

        return (
          <div data-testid="onerm-display">
            1RM Progress: {change > 0 ? '+' : ''}
            {change.toFixed(2)}%
          </div>
        )
      }

      render(
        <OneRMDisplay isFirstWorkSet currentWeight={145} currentReps={10} />
      )

      // Dr. Muscle formula: (0.0333 * 10) * 145 + 145 = 193.285
      // Previous: 180, Progress: (193.285 - 180) / 180 * 100 = 7.38%
      expect(screen.getByTestId('onerm-display')).toHaveTextContent(
        '1RM Progress: +7.38%'
      )
    })

    it('should not display 1RM percentage for warmup sets', () => {
      function OneRMDisplay({ isFirstWorkSet }: any) {
        if (!isFirstWorkSet) return null
        return <div data-testid="onerm-display">1RM Display</div>
      }

      render(<OneRMDisplay isFirstWorkSet={false} />)

      expect(screen.queryByTestId('onerm-display')).not.toBeInTheDocument()
    })
  })

  describe('Integration Tests', () => {
    it('should render complete enhanced grid with all features', () => {
      function EnhancedGrid() {
        const warmupCount = 1
        const workSetCount = 4

        return (
          <div>
            <h1>Montréal</h1>

            <div className="grid">
              <div className="active-set">
                <button aria-label="Increase reps">▲</button>
                <button aria-label="Increase weight">▲</button>
                <span>W</span>
                <input value={12} readOnly />
                <span>×</span>
                <input value={20} readOnly />
                <button aria-label="Decrease reps">▼</button>
                <button aria-label="Decrease weight">▼</button>
              </div>
            </div>

            <div className="explainer-box">
              <p>
                {warmupCount} warm-up, {workSetCount} work sets
              </p>
              <p>Last time: 8 × 20 lbs</p>
            </div>

            <button className="save-button">Save set</button>
          </div>
        )
      }

      render(<EnhancedGrid />)

      // Verify all elements are present
      expect(screen.getByText('Montréal')).toBeInTheDocument()
      expect(screen.getAllByLabelText(/Increase/)).toHaveLength(2)
      expect(screen.getAllByLabelText(/Decrease/)).toHaveLength(2)
      expect(screen.getByText('1 warm-up, 4 work sets')).toBeInTheDocument()
      expect(screen.getByText('Last time: 8 × 20 lbs')).toBeInTheDocument()
      expect(screen.getByText('Save set')).toBeInTheDocument()
    })
  })
})
