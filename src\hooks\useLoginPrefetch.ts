import { useCallback, useState } from 'react'
import { usePrefetchUserStats } from './useUserStats'
import { getUserWorkoutProgramInfo } from '@/services/api/workout'
import { workoutApi } from '@/api/workouts'
import { useWorkoutStore } from '@/stores/workoutStore'

interface UseLoginPrefetchReturn {
  // Progress tracking
  progress: number
  isComplete: boolean
  error: string | null
  status: string

  // Individual progress tracking
  programProgress: number
  isProgramError: boolean
  workoutProgress: number
  isWorkoutComplete: boolean

  // Actions
  startPrefetch: () => void
  reset: () => void
}

export function useLoginPrefetch(): UseLoginPrefetchReturn {
  const { prefetch: prefetchUserStats } = usePrefetchUserStats()
  const {
    loadAllExerciseRecommendations,
    setWorkout,
    currentWorkout,
    workoutSession,
  } = useWorkoutStore()
  const [isStarted, setIsStarted] = useState(false)
  const [isComplete, setIsComplete] = useState(false)
  const [workoutProgress, setWorkoutProgress] = useState(0)
  const [isWorkoutComplete, setIsWorkoutComplete] = useState(false)

  // Start prefetch for both user stats and workout data
  const startPrefetch = useCallback(() => {
    setIsStarted(true)

    // Fire and forget - don't await
    // 1. Prefetch user stats
    prefetchUserStats()

    // 2. Prefetch workout program info
    getUserWorkoutProgramInfo()
      .then((programInfo) => {
        setWorkoutProgress(50)

        // 3. Prefetch the full workout details
        // Note: getUserWorkout doesn't take parameters, it gets the current workout
        if (programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate) {
          return workoutApi.getUserWorkout()
        }
        return null
      })
      .then((workoutData) => {
        setWorkoutProgress(100)
        setIsWorkoutComplete(true)

        // 4. Load exercise recommendations in the background
        if (workoutData && workoutData.length > 0) {
          const workout = workoutData[0]

          // Only set the workout if it's different from the current one
          // This prevents unnecessary re-renders on the exercise page
          if (
            workout &&
            !workoutSession &&
            (!currentWorkout || currentWorkout.Id !== workout.Id)
          ) {
            setWorkout(workout)
          }

          // Load recommendations if there are exercises
          if (workout?.Exercises && workout.Exercises.length > 0) {
            // Fire and forget - don't await or block on recommendations
            loadAllExerciseRecommendations().catch((error) => {
              // Log error but don't block the flow
              console.error('Error loading exercise recommendations:', error)
            })
          }
        }
      })
      .catch((error) => {
        // Log error but don't block the flow
        console.error('Error prefetching workout:', error)
        setWorkoutProgress(100)
        setIsWorkoutComplete(true)
      })

    // Mark overall completion after a short delay
    setTimeout(() => {
      setIsComplete(true)
    }, 150)
  }, [
    prefetchUserStats,
    loadAllExerciseRecommendations,
    setWorkout,
    currentWorkout,
    workoutSession,
  ])

  // Reset state
  const reset = useCallback(() => {
    setIsStarted(false)
    setIsComplete(false)
    setWorkoutProgress(0)
    setIsWorkoutComplete(false)
  }, [])

  // Calculate overall progress
  let progress = 0
  if (isStarted && !isComplete) {
    // Average of stats (50%) and workout progress
    progress = Math.round((50 + workoutProgress) / 2)
  } else if (isComplete) {
    progress = 100
  }

  // Determine status based on state
  let status = 'Starting...'
  if (isComplete) {
    status = 'Ready!'
  } else if (isStarted) {
    if (workoutProgress === 0) {
      status = 'Loading stats...'
    } else if (workoutProgress < 100) {
      status = 'Loading workout...'
    } else {
      status = 'Almost ready...'
    }
  }

  return {
    // State
    progress,
    isComplete,
    error: null, // Prefetch doesn't return errors
    status,

    // Individual progress (keeping for backwards compatibility)
    programProgress: progress,
    isProgramError: false,
    workoutProgress,
    isWorkoutComplete,

    // Actions
    startPrefetch,
    reset,
  }
}
