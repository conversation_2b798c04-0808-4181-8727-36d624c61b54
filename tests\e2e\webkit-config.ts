import { devices } from '@playwright/test'

/**
 * WebKit-specific configuration for CI stability
 * Enhanced with memory management and error recovery
 */
export const webkitLaunchOptions = {
  timeout: 90000, // Reduced to 90 seconds for faster feedback
  slowMo: 100, // Reduced delay for faster execution
  headless: true,
  args: [], // Keep empty - WebKit doesn't support Chrome/Chromium flags
  // Environment variables for WebKit stability
  env: {
    ...process.env,
    WEBKIT_DISABLE_COMPOSITING: '1',
    WEBKIT_FORCE_COMPOSITING_MODE: '0',
  },
  // Simplified stability options
  chromiumSandbox: false,
  handleSIGINT: false,
  handleSIGTERM: false,
  devtools: false,
  ignoreHTTPSErrors: true,
}

export const webkitContextOptions = {
  viewport: { width: 390, height: 844 },
  reducedMotion: 'reduce' as const,
  forcedColors: 'none' as const,
  colorScheme: 'light' as const,
  serviceWorkers: 'block' as const,
  locale: 'en-US',
  permissions: [] as string[],
  bypassCSP: true,
  ignoreHTTPSErrors: true,
  javaScriptEnabled: true,
  strictSelectors: false,
  timeout: 120000, // Reduced to 2 minutes for context operations
}

export const webkitTestOptions = {
  // Reasonable timeouts for WebKit
  actionTimeout: 30000, // 30 seconds for actions
  navigationTimeout: 60000, // 60 seconds for navigation
  // Minimal recording for stability
  screenshot: 'only-on-failure' as const,
  video: 'off' as const, // Disable video recording for stability
  trace: 'retain-on-failure' as const,
  // Additional headers for debugging
  extraHTTPHeaders: {
    'X-WebKit-Test': 'true',
    'Cache-Control': 'no-cache',
  },
}

export const webkitProjectConfig = {
  name: 'Mobile Safari',
  use: {
    ...devices['iPhone 13'],
    viewport: { width: 390, height: 844 },
    hasTouch: true,
    isMobile: true,
    launchOptions: webkitLaunchOptions,
    contextOptions: webkitContextOptions,
    ...webkitTestOptions,
  },
  retries: 3, // Reduced retries to prevent resource exhaustion
  workers: 1, // Single worker for WebKit stability
  timeout: 120000, // 2 minutes per test
  expect: {
    timeout: 30000, // 30 seconds for assertions
  },
}
